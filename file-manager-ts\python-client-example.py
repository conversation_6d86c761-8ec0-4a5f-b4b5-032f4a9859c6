#!/usr/bin/env python3
"""
Python WebSocket 客户端示例
用于连接到文件管理器 WebSocket 服务器

需要安装: pip install websocket-client
"""

import json
import time
import websocket
from datetime import datetime

class FileManagerClient:
    def __init__(self, url="ws://localhost:8080"):
        self.url = url
        self.ws = None
        self.message_id = 0
        
    def generate_id(self):
        self.message_id += 1
        return f"py_client_{self.message_id}_{int(time.time() * 1000)}"
    
    def connect(self):
        """连接到WebSocket服务器"""
        try:
            self.ws = websocket.WebSocket()
            self.ws.connect(self.url)
            print(f"✅ 已连接到 {self.url}")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.ws:
            self.ws.close()
            print("🔚 连接已关闭")
    
    def send_message(self, action, data):
        """发送消息到服务器"""
        if not self.ws:
            print("❌ 未连接到服务器")
            return None
            
        message = {
            "id": self.generate_id(),
            "type": "request",
            "action": action,
            "data": data
        }
        
        try:
            print(f"📤 发送: {action}")
            self.ws.send(json.dumps(message))
            
            # 接收响应
            response = self.ws.recv()
            result = json.loads(response)
            print(f"📨 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
            
        except Exception as e:
            print(f"❌ 发送消息失败: {e}")
            return None
    
    def create_file(self, path, content, encoding="utf8"):
        """创建文件"""
        return self.send_message("create", {
            "path": path,
            "content": content,
            "encoding": encoding
        })
    
    def read_file(self, path, encoding="utf8"):
        """读取文件"""
        return self.send_message("read", {
            "path": path,
            "encoding": encoding
        })
    
    def update_file(self, path, content, encoding="utf8"):
        """更新文件"""
        return self.send_message("update", {
            "path": path,
            "content": content,
            "encoding": encoding
        })
    
    def delete_file(self, path, recursive=False):
        """删除文件或目录"""
        return self.send_message("delete", {
            "path": path,
            "recursive": recursive
        })
    
    def list_directory(self, path=".", recursive=False):
        """列出目录内容"""
        return self.send_message("list", {
            "path": path,
            "recursive": recursive
        })
    
    def create_directory(self, path):
        """创建目录"""
        return self.send_message("mkdir", {
            "path": path
        })

def main():
    """主函数 - 演示各种文件操作"""
    print("🐍 Python WebSocket 文件管理器客户端")
    print("=" * 50)
    
    # 创建客户端并连接
    client = FileManagerClient()
    if not client.connect():
        return
    
    try:
        # 演示各种操作
        print("\n🧪 开始测试文件操作...")
        
        # 1. 列出当前目录
        print("\n1️⃣ 列出当前目录:")
        client.list_directory(".")
        
        # 2. 创建测试文件
        print("\n2️⃣ 创建测试文件:")
        test_content = f"""Python WebSocket 测试文件
创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

这个文件是通过 Python WebSocket 客户端创建的。

功能测试:
✅ 文件创建
✅ 中文支持
✅ 时间戳
"""
        client.create_file("python-test.txt", test_content)
        
        # 3. 读取文件
        print("\n3️⃣ 读取刚创建的文件:")
        client.read_file("python-test.txt")
        
        # 4. 更新文件
        print("\n4️⃣ 更新文件内容:")
        updated_content = test_content + f"\n\n更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n文件已通过 Python 客户端更新。"
        client.update_file("python-test.txt", updated_content)
        
        # 5. 创建目录
        print("\n5️⃣ 创建测试目录:")
        client.create_directory("python-test-dir")
        
        # 6. 在新目录中创建文件
        print("\n6️⃣ 在新目录中创建文件:")
        client.create_file("python-test-dir/nested-file.txt", "这是嵌套目录中的文件\n由 Python 客户端创建")
        
        # 7. 递归列出目录
        print("\n7️⃣ 递归列出目录:")
        client.list_directory(".", recursive=True)
        
        print("\n✅ 所有测试完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    finally:
        # 清理并断开连接
        client.disconnect()

if __name__ == "__main__":
    main()
