# 使用指南

## 🚀 快速开始

### 1. 启动服务器
```bash
cd file-manager-ts
npm install
npm run build
npm start
```

服务器将在端口 8080 启动，你会看到类似以下的输出：
```
WebSocket server started on port 8080
File operations base path: E:\deepseek-engineer\file-manager-ts
==================================================
🚀 File Manager WebSocket Server
==================================================
📡 Port: 8080
📁 Base Path: E:\deepseek-engineer\file-manager-ts
🔗 WebSocket URL: ws://localhost:8080
==================================================
```

### 2. 使用 Web 客户端
在浏览器中打开 `client.html` 文件：
```
file:///E:/deepseek-engineer/file-manager-ts/client.html
```

### 3. 测试连接
1. 点击"连接"按钮
2. 状态应该显示为"已连接"
3. 在响应日志中应该看到连接成功的消息

## 📋 操作示例

### 创建文件
1. 在"创建文件"区域输入：
   - 文件路径: `test.txt`
   - 文件内容: `Hello, World!`
2. 点击"创建文件"按钮
3. 查看响应日志确认操作成功

### 读取文件
1. 在"读取文件"区域输入：
   - 文件路径: `test.txt`
2. 点击"读取文件"按钮
3. 在响应日志中查看文件内容

### 列出目录
1. 在"列出目录"区域输入：
   - 目录路径: `.` (当前目录)
2. 点击"列出目录"按钮
3. 查看当前目录下的所有文件和文件夹

### 创建目录
1. 在"创建目录"区域输入：
   - 目录路径: `test-folder`
2. 点击"创建目录"按钮
3. 使用"列出目录"功能验证目录已创建

### 更新文件
1. 在"更新文件"区域输入：
   - 文件路径: `test.txt`
   - 新内容: `Updated content!`
2. 点击"更新文件"按钮
3. 使用"读取文件"功能验证内容已更新

### 删除文件
1. 在"删除文件/目录"区域输入：
   - 路径: `test.txt`
2. 点击"删除"按钮
3. 确认删除操作
4. 使用"列出目录"功能验证文件已删除

## 🔧 高级功能

### 递归操作
- **递归列出目录**: 勾选"递归列出"复选框，可以列出所有子目录的内容
- **递归删除目录**: 勾选"递归删除"复选框，可以删除非空目录

### 路径规则
- 所有路径都相对于服务器的基础路径
- 使用 `/` 或 `\` 作为路径分隔符
- 支持嵌套路径，如 `folder/subfolder/file.txt`

### 编码支持
- 默认使用 UTF-8 编码
- 支持中文和其他 Unicode 字符

## 🛡️ 安全注意事项

1. **路径限制**: 只能访问服务器基础路径内的文件
2. **文件大小**: 默认限制文件大小为 10MB
3. **本地使用**: 此工具仅适用于本地开发，不要暴露到公网

## 🐛 故障排除

### 连接失败
- 确保服务器正在运行
- 检查端口 8080 是否被占用
- 确认防火墙设置

### 文件操作失败
- 检查文件路径是否正确
- 确认有足够的文件系统权限
- 查看服务器控制台的错误信息

### 浏览器兼容性
- 推荐使用现代浏览器（Chrome、Firefox、Edge）
- 确保浏览器支持 WebSocket

## 📊 消息格式参考

### 请求格式
```json
{
  "id": "unique_id",
  "type": "request",
  "action": "create|read|update|delete|list|mkdir",
  "data": {
    "path": "file/path",
    "content": "content",
    "recursive": true,
    "encoding": "utf8"
  }
}
```

### 响应格式
```json
{
  "id": "unique_id",
  "type": "response",
  "data": {
    "success": true,
    "message": "操作成功",
    "data": "返回数据"
  }
}
```

## 🎯 最佳实践

1. **备份重要文件**: 在进行删除操作前备份重要文件
2. **测试路径**: 先使用"列出目录"确认路径正确
3. **小文件优先**: 先测试小文件，确认功能正常
4. **监控日志**: 关注响应日志中的错误信息
5. **优雅关闭**: 使用 Ctrl+C 正确关闭服务器
