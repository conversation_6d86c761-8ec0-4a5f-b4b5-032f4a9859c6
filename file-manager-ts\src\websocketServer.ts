import * as WebSocket from 'ws';
import { FileOperations } from './fileOperations';
import { WebSocketMessage, FileOperationRequest } from './types';
import { generateId, formatError } from './utils';

export class WebSocketServer {
  private wss: WebSocket.Server;
  private fileOps: FileOperations;
  private port: number;

  constructor(port: number = 8080, basePath?: string) {
    this.port = port;
    this.fileOps = new FileOperations(basePath);
    this.wss = new WebSocket.Server({ port: this.port });
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.wss.on('connection', (ws: WebSocket) => {
      console.log('New client connected');
      
      // 发送连接确认消息
      this.sendMessage(ws, {
        id: generateId(),
        type: 'response',
        data: { message: 'Connected to file manager WebSocket server' }
      });

      ws.on('message', async (data: WebSocket.Data) => {
        try {
          const message: WebSocketMessage = JSON.parse(data.toString());
          await this.handleMessage(ws, message);
        } catch (error) {
          this.sendError(ws, 'Invalid message format', formatError(error));
        }
      });

      ws.on('close', () => {
        console.log('Client disconnected');
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
      });
    });

    this.wss.on('error', (error) => {
      console.error('WebSocket Server error:', error);
    });
  }

  private async handleMessage(ws: WebSocket, message: WebSocketMessage): Promise<void> {
    const { id, action, data } = message;

    if (!action) {
      this.sendError(ws, 'Missing action', undefined, id);
      return;
    }

    try {
      let result;
      const request: FileOperationRequest = data;

      switch (action) {
        case 'create':
          if (!request.content) {
            this.sendError(ws, 'Content is required for create operation', undefined, id);
            return;
          }
          result = await this.fileOps.createFile(request as any);
          break;
        case 'read':
          result = await this.fileOps.readFile(request);
          break;
        case 'update':
          if (!request.content) {
            this.sendError(ws, 'Content is required for update operation', undefined, id);
            return;
          }
          result = await this.fileOps.updateFile(request as any);
          break;
        case 'delete':
          result = await this.fileOps.deleteFile(request);
          break;
        case 'list':
          result = await this.fileOps.listDirectory(request);
          break;
        case 'mkdir':
          result = await this.fileOps.createDirectory(request.path);
          break;
        default:
          this.sendError(ws, `Unknown action: ${action}`, undefined, id);
          return;
      }

      this.sendMessage(ws, {
        id: id || generateId(),
        type: 'response',
        data: result
      });

    } catch (error) {
      this.sendError(ws, 'Operation failed', formatError(error), id);
    }
  }

  private sendMessage(ws: WebSocket, message: WebSocketMessage): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  private sendError(ws: WebSocket, error: string, details?: string, id?: string): void {
    this.sendMessage(ws, {
      id: id || generateId(),
      type: 'error',
      error,
      data: details ? { details } : undefined
    });
  }

  public start(): void {
    console.log(`WebSocket server started on port ${this.port}`);
    console.log(`File operations base path: ${this.fileOps['basePath']}`);
  }

  public stop(): void {
    this.wss.close();
    console.log('WebSocket server stopped');
  }

  public getConnectedClients(): number {
    return this.wss.clients.size;
  }
}
