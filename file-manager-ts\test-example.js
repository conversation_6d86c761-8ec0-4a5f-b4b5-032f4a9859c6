// 简单的 WebSocket 客户端测试脚本
const WebSocket = require('ws');

const ws = new WebSocket('ws://localhost:8080');

let messageId = 0;

function generateId() {
    return 'test_' + (++messageId) + '_' + Date.now();
}

function sendMessage(action, data) {
    const message = {
        id: generateId(),
        type: 'request',
        action: action,
        data: data
    };
    
    console.log('📤 发送:', JSON.stringify(message, null, 2));
    ws.send(JSON.stringify(message));
}

ws.on('open', function() {
    console.log('✅ WebSocket 连接已建立');
    
    // 测试序列
    setTimeout(() => {
        console.log('\n🧪 开始测试文件操作...\n');
        
        // 1. 创建测试文件
        sendMessage('create', {
            path: 'test-file.txt',
            content: 'Hello from WebSocket!\n这是一个测试文件。'
        });
    }, 1000);
    
    setTimeout(() => {
        // 2. 读取文件
        sendMessage('read', {
            path: 'test-file.txt'
        });
    }, 2000);
    
    setTimeout(() => {
        // 3. 更新文件
        sendMessage('update', {
            path: 'test-file.txt',
            content: 'Updated content!\n文件已更新。\n时间: ' + new Date().toLocaleString()
        });
    }, 3000);
    
    setTimeout(() => {
        // 4. 列出当前目录
        sendMessage('list', {
            path: '.',
            recursive: false
        });
    }, 4000);
    
    setTimeout(() => {
        // 5. 创建目录
        sendMessage('mkdir', {
            path: 'test-directory'
        });
    }, 5000);
    
    setTimeout(() => {
        // 6. 在新目录中创建文件
        sendMessage('create', {
            path: 'test-directory/nested-file.txt',
            content: '这是嵌套目录中的文件'
        });
    }, 6000);
    
    setTimeout(() => {
        // 7. 递归列出目录
        sendMessage('list', {
            path: '.',
            recursive: true
        });
    }, 7000);
    
    setTimeout(() => {
        console.log('\n✅ 测试完成，5秒后关闭连接...');
        setTimeout(() => {
            ws.close();
        }, 5000);
    }, 8000);
});

ws.on('message', function(data) {
    const message = JSON.parse(data.toString());
    console.log('📨 收到响应:', JSON.stringify(message, null, 2));
    console.log('---');
});

ws.on('close', function() {
    console.log('❌ WebSocket 连接已关闭');
    process.exit(0);
});

ws.on('error', function(error) {
    console.error('❌ WebSocket 错误:', error);
});
