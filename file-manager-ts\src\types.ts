export interface WebSocketMessage {
  id: string;
  type: 'request' | 'response' | 'error';
  action?: string;
  data?: any;
  error?: string;
}

export interface FileOperationRequest {
  action: 'create' | 'read' | 'update' | 'delete' | 'list' | 'mkdir';
  path: string;
  content?: string;
  options?: {
    recursive?: boolean;
    encoding?: string;
  };
}

export interface FileOperationResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

export interface FileInfo {
  name: string;
  path: string;
  isDirectory: boolean;
  size?: number;
  lastModified?: Date;
}

export interface DirectoryListing {
  path: string;
  files: FileInfo[];
}

export interface CreateFileRequest {
  path: string;
  content: string;
  encoding?: string;
}

export interface UpdateFileRequest {
  path: string;
  content: string;
  encoding?: string;
}

export interface DeleteRequest {
  path: string;
  recursive?: boolean;
}

export interface ReadFileRequest {
  path: string;
  encoding?: string;
}

export interface ListDirectoryRequest {
  path: string;
  recursive?: boolean;
}
