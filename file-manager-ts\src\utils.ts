import * as path from 'path';
import * as fs from 'fs-extra';

/**
 * 验证文件路径是否安全
 * 防止目录遍历攻击
 */
export function validatePath(filePath: string, basePath: string = process.cwd()): boolean {
  try {
    const resolvedPath = path.resolve(basePath, filePath);
    const normalizedBasePath = path.resolve(basePath);
    
    // 确保解析后的路径在基础路径内
    return resolvedPath.startsWith(normalizedBasePath);
  } catch (error) {
    return false;
  }
}

/**
 * 规范化文件路径
 */
export function normalizePath(filePath: string, basePath: string = process.cwd()): string {
  return path.resolve(basePath, filePath);
}

/**
 * 检查文件或目录是否存在
 */
export async function pathExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取文件统计信息
 */
export async function getFileStats(filePath: string): Promise<fs.Stats | null> {
  try {
    return await fs.stat(filePath);
  } catch {
    return null;
  }
}

/**
 * 生成唯一ID
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}

/**
 * 检查文件大小是否超过限制
 */
export async function checkFileSize(filePath: string, maxSize: number = 10 * 1024 * 1024): Promise<boolean> {
  const stats = await getFileStats(filePath);
  if (!stats) return false;
  return stats.size <= maxSize;
}

/**
 * 格式化错误消息
 */
export function formatError(error: any): string {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}

/**
 * 检查路径是否为目录
 */
export async function isDirectory(filePath: string): Promise<boolean> {
  try {
    const stats = await fs.stat(filePath);
    return stats.isDirectory();
  } catch {
    return false;
  }
}

/**
 * 检查路径是否为文件
 */
export async function isFile(filePath: string): Promise<boolean> {
  try {
    const stats = await fs.stat(filePath);
    return stats.isFile();
  } catch {
    return false;
  }
}
