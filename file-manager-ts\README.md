# 文件管理器 WebSocket 服务

这是一个基于 TypeScript 和 WebSocket 的本地文件管理系统，允许通过 WebSocket 连接对本地文件进行增删改查操作。

## 🚀 功能特性

- **文件操作**：创建、读取、更新、删除文件
- **目录操作**：创建目录、列出目录内容（支持递归）
- **安全性**：路径验证、防止目录遍历攻击
- **实时通信**：基于 WebSocket 的实时双向通信
- **类型安全**：完全使用 TypeScript 开发
- **Web 客户端**：提供友好的 HTML 界面

## 📋 支持的操作

| 操作 | 描述 | 参数 |
|------|------|------|
| `create` | 创建新文件 | `path`, `content`, `encoding?` |
| `read` | 读取文件内容 | `path`, `encoding?` |
| `update` | 更新文件内容 | `path`, `content`, `encoding?` |
| `delete` | 删除文件或目录 | `path`, `recursive?` |
| `list` | 列出目录内容 | `path`, `recursive?` |
| `mkdir` | 创建目录 | `path` |

## 🛠️ 安装和运行

### 1. 安装依赖
```bash
npm install
```

### 2. 编译 TypeScript
```bash
npm run build
```

### 3. 启动服务器
```bash
npm start
# 或者
node dist/index.js
```

### 4. 使用 Web 客户端
在浏览器中打开 `client.html` 文件，即可通过图形界面操作文件。

## 🔧 配置

可以通过环境变量配置服务器：

```bash
# 设置端口（默认：8080）
export PORT=3000

# 设置基础路径（默认：当前工作目录）
export BASE_PATH=/path/to/your/files

# 启动服务器
npm start
```

## 📡 WebSocket API

### 连接
```
ws://localhost:8080
```

### 消息格式

**请求消息**：
```json
{
  "id": "unique_message_id",
  "type": "request",
  "action": "create|read|update|delete|list|mkdir",
  "data": {
    "path": "file/path",
    "content": "file content",
    "recursive": true,
    "encoding": "utf8"
  }
}
```

**响应消息**：
```json
{
  "id": "unique_message_id",
  "type": "response",
  "data": {
    "success": true,
    "message": "操作成功",
    "data": "返回的数据"
  }
}
```

**错误消息**：
```json
{
  "id": "unique_message_id",
  "type": "error",
  "error": "错误描述",
  "data": {
    "details": "详细错误信息"
  }
}
```

## 📝 使用示例

### 创建文件
```json
{
  "id": "msg_1",
  "type": "request",
  "action": "create",
  "data": {
    "path": "test.txt",
    "content": "Hello, World!"
  }
}
```

### 读取文件
```json
{
  "id": "msg_2",
  "type": "request",
  "action": "read",
  "data": {
    "path": "test.txt"
  }
}
```

### 列出目录
```json
{
  "id": "msg_3",
  "type": "request",
  "action": "list",
  "data": {
    "path": ".",
    "recursive": false
  }
}
```

## 🛡️ 安全特性

- **路径验证**：防止访问基础路径之外的文件
- **文件大小限制**：默认限制文件大小为 10MB
- **类型检查**：严格的 TypeScript 类型检查
- **错误处理**：完善的错误处理和日志记录

## 🏗️ 项目结构

```
file-manager-ts/
├── src/
│   ├── index.ts              # 应用入口
│   ├── websocketServer.ts    # WebSocket 服务器
│   ├── fileOperations.ts     # 文件操作逻辑
│   ├── types.ts              # 类型定义
│   └── utils.ts              # 工具函数
├── dist/                     # 编译后的 JavaScript 文件
├── client.html               # Web 客户端界面
├── package.json              # 项目配置
├── tsconfig.json             # TypeScript 配置
└── README.md                 # 项目说明
```

## 🔄 开发模式

```bash
# 监听文件变化并自动编译
npm run watch

# 在另一个终端启动服务器
npm start
```

## ⚠️ 注意事项

1. **安全性**：此工具仅用于本地开发，不建议在生产环境中暴露到公网
2. **权限**：确保运行用户有足够的文件系统权限
3. **路径**：所有路径都相对于服务器启动时的基础路径
4. **编码**：默认使用 UTF-8 编码，可通过参数指定其他编码

## 📄 许可证

MIT License
