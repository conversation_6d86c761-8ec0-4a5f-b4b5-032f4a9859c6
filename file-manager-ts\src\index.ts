import { WebSocketServer } from './websocketServer';
import * as path from 'path';

// 配置参数
const PORT = process.env.PORT ? parseInt(process.env.PORT) : 8080;
const BASE_PATH = process.env.BASE_PATH || process.cwd();

// 创建并启动WebSocket服务器
const server = new WebSocketServer(PORT, BASE_PATH);

// 启动服务器
server.start();

// 优雅关闭处理
process.on('SIGINT', () => {
  console.log('\nReceived SIGINT, shutting down gracefully...');
  server.stop();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\nReceived SIGTERM, shutting down gracefully...');
  server.stop();
  process.exit(0);
});

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  server.stop();
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  server.stop();
  process.exit(1);
});

console.log('='.repeat(50));
console.log('🚀 File Manager WebSocket Server');
console.log('='.repeat(50));
console.log(`📡 Port: ${PORT}`);
console.log(`📁 Base Path: ${BASE_PATH}`);
console.log(`🔗 WebSocket URL: ws://localhost:${PORT}`);
console.log('='.repeat(50));
console.log('📋 Available Actions:');
console.log('  • create - Create a new file');
console.log('  • read   - Read file content');
console.log('  • update - Update file content');
console.log('  • delete - Delete file or directory');
console.log('  • list   - List directory contents');
console.log('  • mkdir  - Create directory');
console.log('='.repeat(50));
console.log('Press Ctrl+C to stop the server');
console.log('='.repeat(50));
