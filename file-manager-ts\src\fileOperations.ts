import * as fs from 'fs-extra';
import * as path from 'path';
import {
  FileOperationResponse,
  FileInfo,
  DirectoryListing,
  CreateFileRequest,
  UpdateFileRequest,
  DeleteRequest,
  ReadFileRequest,
  ListDirectoryRequest
} from './types';
import {
  validatePath,
  normalizePath,
  pathExists,
  getFileStats,
  formatError,
  isDirectory,
  isFile,
  checkFileSize
} from './utils';

export class FileOperations {
  private basePath: string;
  private maxFileSize: number;

  constructor(basePath: string = process.cwd(), maxFileSize: number = 10 * 1024 * 1024) {
    this.basePath = path.resolve(basePath);
    this.maxFileSize = maxFileSize;
  }

  /**
   * 创建文件
   */
  async createFile(request: CreateFileRequest): Promise<FileOperationResponse> {
    try {
      const { path: filePath, content, encoding = 'utf8' } = request;
      
      if (!validatePath(filePath, this.basePath)) {
        return {
          success: false,
          error: 'Invalid file path'
        };
      }

      const fullPath = normalizePath(filePath, this.basePath);
      
      // 检查文件是否已存在
      if (await pathExists(fullPath)) {
        return {
          success: false,
          error: 'File already exists'
        };
      }

      // 确保目录存在
      await fs.ensureDir(path.dirname(fullPath));
      
      // 写入文件
      await fs.writeFile(fullPath, content, encoding as BufferEncoding);
      
      return {
        success: true,
        message: 'File created successfully',
        data: { path: fullPath }
      };
    } catch (error) {
      return {
        success: false,
        error: formatError(error)
      };
    }
  }

  /**
   * 读取文件
   */
  async readFile(request: ReadFileRequest): Promise<FileOperationResponse> {
    try {
      const { path: filePath, encoding = 'utf8' } = request;
      
      if (!validatePath(filePath, this.basePath)) {
        return {
          success: false,
          error: 'Invalid file path'
        };
      }

      const fullPath = normalizePath(filePath, this.basePath);
      
      if (!(await pathExists(fullPath))) {
        return {
          success: false,
          error: 'File not found'
        };
      }

      if (!(await isFile(fullPath))) {
        return {
          success: false,
          error: 'Path is not a file'
        };
      }

      // 检查文件大小
      if (!(await checkFileSize(fullPath, this.maxFileSize))) {
        return {
          success: false,
          error: 'File too large'
        };
      }

      const content = await fs.readFile(fullPath, encoding as BufferEncoding);
      
      return {
        success: true,
        data: { content, path: fullPath }
      };
    } catch (error) {
      return {
        success: false,
        error: formatError(error)
      };
    }
  }

  /**
   * 更新文件
   */
  async updateFile(request: UpdateFileRequest): Promise<FileOperationResponse> {
    try {
      const { path: filePath, content, encoding = 'utf8' } = request;

      if (!validatePath(filePath, this.basePath)) {
        return {
          success: false,
          error: 'Invalid file path'
        };
      }

      const fullPath = normalizePath(filePath, this.basePath);

      if (!(await pathExists(fullPath))) {
        return {
          success: false,
          error: 'File not found'
        };
      }

      if (!(await isFile(fullPath))) {
        return {
          success: false,
          error: 'Path is not a file'
        };
      }

      await fs.writeFile(fullPath, content, encoding as BufferEncoding);

      return {
        success: true,
        message: 'File updated successfully',
        data: { path: fullPath }
      };
    } catch (error) {
      return {
        success: false,
        error: formatError(error)
      };
    }
  }

  /**
   * 删除文件或目录
   */
  async deleteFile(request: DeleteRequest): Promise<FileOperationResponse> {
    try {
      const { path: filePath, recursive = false } = request;

      if (!validatePath(filePath, this.basePath)) {
        return {
          success: false,
          error: 'Invalid file path'
        };
      }

      const fullPath = normalizePath(filePath, this.basePath);

      if (!(await pathExists(fullPath))) {
        return {
          success: false,
          error: 'File or directory not found'
        };
      }

      const isDir = await isDirectory(fullPath);

      if (isDir && !recursive) {
        return {
          success: false,
          error: 'Cannot delete directory without recursive flag'
        };
      }

      await fs.remove(fullPath);

      return {
        success: true,
        message: `${isDir ? 'Directory' : 'File'} deleted successfully`,
        data: { path: fullPath }
      };
    } catch (error) {
      return {
        success: false,
        error: formatError(error)
      };
    }
  }

  /**
   * 创建目录
   */
  async createDirectory(dirPath: string): Promise<FileOperationResponse> {
    try {
      if (!validatePath(dirPath, this.basePath)) {
        return {
          success: false,
          error: 'Invalid directory path'
        };
      }

      const fullPath = normalizePath(dirPath, this.basePath);

      if (await pathExists(fullPath)) {
        return {
          success: false,
          error: 'Directory already exists'
        };
      }

      await fs.ensureDir(fullPath);

      return {
        success: true,
        message: 'Directory created successfully',
        data: { path: fullPath }
      };
    } catch (error) {
      return {
        success: false,
        error: formatError(error)
      };
    }
  }

  /**
   * 列出目录内容
   */
  async listDirectory(request: ListDirectoryRequest): Promise<FileOperationResponse> {
    try {
      const { path: dirPath, recursive = false } = request;

      if (!validatePath(dirPath, this.basePath)) {
        return {
          success: false,
          error: 'Invalid directory path'
        };
      }

      const fullPath = normalizePath(dirPath, this.basePath);

      if (!(await pathExists(fullPath))) {
        return {
          success: false,
          error: 'Directory not found'
        };
      }

      if (!(await isDirectory(fullPath))) {
        return {
          success: false,
          error: 'Path is not a directory'
        };
      }

      const files: FileInfo[] = [];

      if (recursive) {
        await this.listDirectoryRecursive(fullPath, files);
      } else {
        const entries = await fs.readdir(fullPath);

        for (const entry of entries) {
          const entryPath = path.join(fullPath, entry);
          const stats = await getFileStats(entryPath);

          if (stats) {
            files.push({
              name: entry,
              path: path.relative(this.basePath, entryPath),
              isDirectory: stats.isDirectory(),
              size: stats.isFile() ? stats.size : undefined,
              lastModified: stats.mtime
            });
          }
        }
      }

      const result: DirectoryListing = {
        path: path.relative(this.basePath, fullPath),
        files
      };

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: formatError(error)
      };
    }
  }

  /**
   * 递归列出目录内容
   */
  private async listDirectoryRecursive(dirPath: string, files: FileInfo[]): Promise<void> {
    const entries = await fs.readdir(dirPath);

    for (const entry of entries) {
      const entryPath = path.join(dirPath, entry);
      const stats = await getFileStats(entryPath);

      if (stats) {
        files.push({
          name: entry,
          path: path.relative(this.basePath, entryPath),
          isDirectory: stats.isDirectory(),
          size: stats.isFile() ? stats.size : undefined,
          lastModified: stats.mtime
        });

        if (stats.isDirectory()) {
          await this.listDirectoryRecursive(entryPath, files);
        }
      }
    }
  }
}
