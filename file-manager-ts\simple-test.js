// 简单测试脚本
const WebSocket = require('ws');

console.log('🔗 正在连接到 WebSocket 服务器...');

const ws = new WebSocket('ws://localhost:8080');

ws.on('open', function() {
    console.log('✅ 连接成功！');
    
    // 发送一个简单的列表请求
    const message = {
        id: 'test_1',
        type: 'request',
        action: 'list',
        data: {
            path: '.',
            recursive: false
        }
    };
    
    console.log('📤 发送列表请求...');
    ws.send(JSON.stringify(message));
});

ws.on('message', function(data) {
    console.log('📨 收到响应:');
    const message = JSON.parse(data.toString());
    console.log(JSON.stringify(message, null, 2));
    
    // 收到响应后关闭连接
    setTimeout(() => {
        console.log('🔚 关闭连接');
        ws.close();
    }, 1000);
});

ws.on('close', function() {
    console.log('❌ 连接已关闭');
    process.exit(0);
});

ws.on('error', function(error) {
    console.error('❌ 连接错误:', error.message);
    process.exit(1);
});
