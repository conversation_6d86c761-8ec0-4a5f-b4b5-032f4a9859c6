<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件管理器 WebSocket 客户端</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .operations {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <h1>🗂️ 文件管理器 WebSocket 客户端</h1>
    
    <div class="container">
        <div id="status" class="status disconnected">未连接</div>
        <button id="connectBtn" onclick="connect()">连接</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
    </div>

    <div class="operations">
        <!-- 创建文件 -->
        <div class="container">
            <h3>📄 创建文件</h3>
            <div class="form-group">
                <label>文件路径:</label>
                <input type="text" id="createPath" placeholder="例如: test.txt">
            </div>
            <div class="form-group">
                <label>文件内容:</label>
                <textarea id="createContent" placeholder="输入文件内容..."></textarea>
            </div>
            <button onclick="createFile()">创建文件</button>
        </div>

        <!-- 读取文件 -->
        <div class="container">
            <h3>📖 读取文件</h3>
            <div class="form-group">
                <label>文件路径:</label>
                <input type="text" id="readPath" placeholder="例如: test.txt">
            </div>
            <button onclick="readFile()">读取文件</button>
        </div>

        <!-- 更新文件 -->
        <div class="container">
            <h3>✏️ 更新文件</h3>
            <div class="form-group">
                <label>文件路径:</label>
                <input type="text" id="updatePath" placeholder="例如: test.txt">
            </div>
            <div class="form-group">
                <label>新内容:</label>
                <textarea id="updateContent" placeholder="输入新的文件内容..."></textarea>
            </div>
            <button onclick="updateFile()">更新文件</button>
        </div>

        <!-- 删除文件 -->
        <div class="container">
            <h3>🗑️ 删除文件/目录</h3>
            <div class="form-group">
                <label>路径:</label>
                <input type="text" id="deletePath" placeholder="例如: test.txt">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="deleteRecursive"> 递归删除（用于目录）
                </label>
            </div>
            <button onclick="deleteFile()">删除</button>
        </div>

        <!-- 列出目录 -->
        <div class="container">
            <h3>📁 列出目录</h3>
            <div class="form-group">
                <label>目录路径:</label>
                <input type="text" id="listPath" placeholder="例如: . (当前目录)">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="listRecursive"> 递归列出
                </label>
            </div>
            <button onclick="listDirectory()">列出目录</button>
        </div>

        <!-- 创建目录 -->
        <div class="container">
            <h3>📂 创建目录</h3>
            <div class="form-group">
                <label>目录路径:</label>
                <input type="text" id="mkdirPath" placeholder="例如: new-folder">
            </div>
            <button onclick="createDirectory()">创建目录</button>
        </div>
    </div>

    <div class="container">
        <h3>📋 响应日志</h3>
        <div id="response" class="response">等待操作...</div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let ws = null;
        let messageId = 0;

        function generateId() {
            return 'msg_' + (++messageId) + '_' + Date.now();
        }

        function connect() {
            try {
                ws = new WebSocket('ws://localhost:8080');
                
                ws.onopen = function() {
                    updateStatus('已连接', true);
                    log('✅ WebSocket 连接已建立');
                };
                
                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    log('📨 收到响应: ' + JSON.stringify(message, null, 2));
                };
                
                ws.onclose = function() {
                    updateStatus('连接已关闭', false);
                    log('❌ WebSocket 连接已关闭');
                };
                
                ws.onerror = function(error) {
                    log('❌ WebSocket 错误: ' + error);
                };
            } catch (error) {
                log('❌ 连接失败: ' + error.message);
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function updateStatus(text, connected) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = text;
            statusEl.className = 'status ' + (connected ? 'connected' : 'disconnected');
            
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
        }

        function sendMessage(action, data) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket 未连接');
                return;
            }
            
            const message = {
                id: generateId(),
                type: 'request',
                action: action,
                data: data
            };
            
            log('📤 发送请求: ' + JSON.stringify(message, null, 2));
            ws.send(JSON.stringify(message));
        }

        function createFile() {
            const path = document.getElementById('createPath').value;
            const content = document.getElementById('createContent').value;
            
            if (!path) {
                alert('请输入文件路径');
                return;
            }
            
            sendMessage('create', {
                path: path,
                content: content || ''
            });
        }

        function readFile() {
            const path = document.getElementById('readPath').value;
            
            if (!path) {
                alert('请输入文件路径');
                return;
            }
            
            sendMessage('read', { path: path });
        }

        function updateFile() {
            const path = document.getElementById('updatePath').value;
            const content = document.getElementById('updateContent').value;
            
            if (!path) {
                alert('请输入文件路径');
                return;
            }
            
            sendMessage('update', {
                path: path,
                content: content || ''
            });
        }

        function deleteFile() {
            const path = document.getElementById('deletePath').value;
            const recursive = document.getElementById('deleteRecursive').checked;
            
            if (!path) {
                alert('请输入路径');
                return;
            }
            
            if (!confirm('确定要删除 "' + path + '" 吗？')) {
                return;
            }
            
            sendMessage('delete', {
                path: path,
                recursive: recursive
            });
        }

        function listDirectory() {
            const path = document.getElementById('listPath').value || '.';
            const recursive = document.getElementById('listRecursive').checked;
            
            sendMessage('list', {
                path: path,
                recursive: recursive
            });
        }

        function createDirectory() {
            const path = document.getElementById('mkdirPath').value;
            
            if (!path) {
                alert('请输入目录路径');
                return;
            }
            
            sendMessage('mkdir', { path: path });
        }

        function log(message) {
            const responseEl = document.getElementById('response');
            const timestamp = new Date().toLocaleTimeString();
            responseEl.textContent += `[${timestamp}] ${message}\n\n`;
            responseEl.scrollTop = responseEl.scrollHeight;
        }

        function clearLog() {
            document.getElementById('response').textContent = '等待操作...';
        }

        // 页面加载时自动连接
        window.onload = function() {
            connect();
        };
    </script>
</body>
</html>
