{"name": "file-manager-websocket", "version": "1.0.0", "description": "WebSocket server for local file operations", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsc && node dist/index.js", "watch": "tsc -w"}, "keywords": ["websocket", "file-manager", "typescript"], "author": "", "license": "MIT", "dependencies": {"ws": "^8.14.2", "fs-extra": "^11.1.1", "path": "^0.12.7"}, "devDependencies": {"@types/node": "^20.8.0", "@types/ws": "^8.5.6", "@types/fs-extra": "^11.0.2", "typescript": "^5.2.2"}}